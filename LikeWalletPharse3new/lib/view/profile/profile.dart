import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/loading.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late final ProfileController profileCtrl;

  @override
  void initState() {
    super.initState();
    profileCtrl = Get.isRegistered<ProfileController>()
        ? Get.find<ProfileController>()
        : Get.put(ProfileController());
  }

  @override
  Widget build(BuildContext cx) {
    return Obx(() => ModalProgressHUD(
      opacity: 0.1,
      inAsyncCall: profileCtrl.isSaving.value,
      progressIndicator: CustomLoading(),
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Scaffold(
          body: SingleChildScrollView(
            child: Stack(
              children: [
                _body(),

                /// ✅ แยก Obx เฉพาะจุด
                Positioned(
                  top: mediaQuery(context, 'height', 219),
                  left: mediaQuery(context, 'width', 63),
                  child: Container(
                    height: MediaQuery.of(context).size.width * 0.24,
                    width: MediaQuery.of(context).size.width * 0.24,
                    decoration: BoxDecoration(
                      color: LikeWalletAppTheme.white.withOpacity(0.9),
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        fit: BoxFit.cover,
                        image: profileCtrl.uploadedFileURL.value == 'noprofile'
                            ? AssetImage(LikeWalletImage.defaultProfile) as ImageProvider
                            : NetworkImage(profileCtrl.uploadedFileURL.value),
                      ),
                      border: Border.all(
                        color: Colors.white,
                        width: MediaQuery.of(context).size.width * 0.002,
                      ),
                    ),
                  ),
                ),

                // ปุ่มเปลี่ยนรูปภาพ
                Positioned(
                  top: mediaQuery(context, 'height', 390),
                  left: mediaQuery(context, 'width', 240.76),
                  child: GestureDetector(
                    onTap: () {
                      profileCtrl.chooseFile().then((data) {
                        if (data) {
                          profileCtrl.uploadFile();
                        }
                      });
                    },
                    child: Container(
                      height: mediaQuery(context, 'width', 80.24),
                      width: mediaQuery(context, 'width', 80.24),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        image: DecorationImage(
                          fit: BoxFit.cover,
                          image: AssetImage(LikeWalletImage.edit_photo),
                        ),
                        border: Border.all(color: LikeWalletAppTheme.white),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ));
  }


  Widget _body() {
    return Column(
      children: <Widget>[_cover(), _bg()],
    );
  }

  Widget _bg() {
    return Container(
      height: MediaQuery.of(context).size.height -
          mediaQuery(context, 'height', 390),
      decoration: BoxDecoration(
        color: const Color(0xff141322),
        boxShadow: [
          BoxShadow(
            color: const Color(0xcc00887f),
            offset: Offset(0, -mediaQuery(context, 'height', 18)),
            spreadRadius: 1,
            blurRadius: mediaQuery(context, 'height', 35),
          ),
        ],
      ),
      child: Column(
        children: [
          _phone(),
//                textFieldProfile(
//                    context, profileCtrl.editPhone, 'profile_phone', 'profile_phone',
//                    active: false),
          _profile(),
          textFieldProfile(
              context, profileCtrl.editFirstName, 'profile_name', 'profile_name'),
          textFieldProfile(
              context, profileCtrl.editLastName, 'profile_lastname', 'profile_lastname'),
          // textFieldProfile(
          //     context, profileCtrl.editEmail, 'profile_email', 'profile_email'),
          textFieldProfile(
              context, profileCtrl.editFacebook, 'profile_facebook', 'profile_facebook'),
          textFieldProfile(context, profileCtrl.editLine, 'profile_line', 'profile_line'),
          _bottomSave(),
          SizedBox(
            height: 18,
          ),
          _bottomDeletion(),
        ],
      ),
    );
  }

  Widget _phone() {
    return Container(
      alignment: Alignment.centerRight,
      padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 65),
          right: mediaQuery(context, 'width', 75)),
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          Text(
            profileCtrl.editPhone.text,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              color: LikeWalletAppTheme.white.withOpacity(0.9),
              letterSpacing: 0.5,
              fontSize: mediaQuery(context, "height", 45),
              fontWeight: FontWeight.w100,
            ),
          ),
          Container(
            child: Text(
              'profile_phone'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                color: LikeWalletAppTheme.white.withOpacity(0.4),
                letterSpacing: 0.5,
                fontSize: mediaQuery(context, "height", 36),
                fontWeight: FontWeight.w100,
              ),
            ),
          ),
          SizedBox(
            height: mediaQuery(context, 'height', 40),
          ),
          // changePhone()
        ],
      ),
    );
  }

  Widget changePhone() {
    return InkWell(
      // onTap: () => Navigator.push(
      //     context,
      //     EnterExitRoute(
      //         exitPage: ProfilePage(),
      //         enterPage: ChangePhone(oldPhone: profileCtrl.editPhone.text))),
      child: Container(
        alignment: Alignment.center,
        width: mediaQuery(context, 'width', 363),
        height: mediaQuery(context, 'height', 159),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(80.0),
          gradient: LinearGradient(
            begin: Alignment(0.0, -1.0),
            end: Alignment(0.0, 1.0),
            colors: [const Color(0x802f3042), const Color(0x8028293b)],
            stops: [0.0, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x80000000),
              offset: Offset(0, mediaQuery(context, 'height', 3)),
              spreadRadius: 0,
              blurRadius: mediaQuery(context, 'height', 6),
            ),
          ],
        ),
        child: Text(
          'profile_change_phone'.tr,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Proxima Nova',
            color: LikeWalletAppTheme.bule1,
            letterSpacing: 0.8,
            fontSize: mediaQuery(context, "height", 33),
            fontWeight: FontWeight.w100,
          ),
        ),
      ),
    );
  }

  Widget _cover() {
    return Container(
      child: Stack(
        clipBehavior: Clip.none,
        children: <Widget>[
          Column(
            children: <Widget>[
              //ชื่อนามสกุลโชว์ในcover
              Container(
                alignment: Alignment.center,
                width: mediaQuery(context, 'width', 1080.0),
                height: mediaQuery(context, 'height', 390),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(1.0, -1.0),
                    end: Alignment(-0.96, 1.08),
                    colors: [const Color(0xff30f3ca), const Color(0xff24c6e4)],
                    stops: [0.0, 1.0],
                  ),
                ),
                child: Obx(() => Text(
                  profileCtrl.firstName.value + '\n' + profileCtrl.lastName.value,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 24.sp,
                      color: LikeWalletAppTheme.white.withOpacity(0.6),
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Proxima Nova'),
                )),
              ),
            ],
          ),
          Positioned(
            top: 50.h,
            left: 20.w,
            child: backButton(context, LikeWalletAppTheme.black),
          ),
        ],
      ),
    );
  }

  Widget _profile() {
    return Padding(
        padding: EdgeInsets.only(
            top: mediaQuery(context, 'hieght', 65),
            left: mediaQuery(context, 'width', 100),
            bottom: mediaQuery(context, 'hieght', 54.27)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: EdgeInsets.only(right: mediaQuery(context, 'width', 10)),
              child: Text(
                'profile_title'.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: LikeWalletAppTheme.bule1_8.withOpacity(0.4),
                  fontWeight: FontWeight.normal,
                  fontFamily: 'Proxima Nova',
                ),
              ),
            ),
          ],
        ));
  }

  Widget _bottomSave() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: mediaQuery(context, 'width', 80)),
      child: Container(
        width: 930.w,
        height: 48.h,
        child: TextButton(
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all<Color>(
                Color(0xff00F1E0),
              ),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  )),
            ),
            onPressed: () {
              profileCtrl.saveProfile();
            },
            child: Text(
              'profile_save'.tr,
              style: TextStyle(
                fontSize: 18.sp,
                color: LikeWalletAppTheme.black,
                fontWeight: FontWeight.normal,
                fontFamily: 'Proxima Nova',
              ),
            )),
      ),
    );
  }
  Widget _bottomDeletion() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: mediaQuery(context, 'width', 80)),
      child: Container(
        width: 930.w,
        height: 48.h,
        child: TextButton(
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all<Color>(
                LikeWalletAppTheme.red,
              ),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  )),
            ),
            onPressed: () {
              // CustomPopup.showErrorDialog(context, 'profile_delete_account_title'.tr, 'profile_delete_account_text'.tr);
            },
            child: Text(
              'delete_account'.tr,
              style: TextStyle(
                fontSize: 18.sp,
                color: LikeWalletAppTheme.black,
                fontWeight: FontWeight.normal,
                fontFamily: 'Proxima Nova',
              ),
            )),
      ),
    );
  }

  textFieldProfile(context, value, String hint, String label,
      {bool active = true}) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: mediaQuery(context, 'height', 63),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: LikeWalletAppTheme.bule2,
          borderRadius: BorderRadius.all(Radius.circular(5.0)),
        ),
        alignment: Alignment.center,
        height: mediaQuery(context, 'height', 156),
        width: mediaQuery(context, 'width', 930),
        child: TextField(
          enabled: active,
          controller: value,
          style: TextStyle(
              fontSize: mediaQuery(context, 'height', 47),
              fontFamily: 'Proxima Nova',
              fontWeight: FontWeight.w100,
              letterSpacing: 0.3,
              color: LikeWalletAppTheme.white),
          decoration: InputDecoration(
            contentPadding: EdgeInsets.only(
                left: MediaQuery.of(context).size.width * 0.03,
                top: MediaQuery.of(context).size.height * 0.03),
            focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                    color: LikeWalletAppTheme.bule1,
                    width: mediaQuery(context, 'width', 1))),
//          contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
            labelText: hint.tr,
            labelStyle: TextStyle(
                letterSpacing: 0.3,
                fontSize: mediaQuery(context, 'height', 50),
                fontFamily: 'Proxima Nova',
                color: LikeWalletAppTheme.white.withOpacity(0.3)),
            border: InputBorder.none,
            enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                    width: mediaQuery(context, 'width', 0.3),
                    color: LikeWalletAppTheme.bule1)),
            hoverColor: Colors.white,
            disabledBorder: InputBorder.none,
            focusColor: Colors.white,
            alignLabelWithHint: true,
            fillColor: Colors.white,
          ),
          keyboardType: TextInputType.text,
        ),
      ),
    );
  }
}






