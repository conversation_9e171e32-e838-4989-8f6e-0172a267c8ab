import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/buyLikepointController/buyLikepointController.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';
import 'package:likewallet/view/transferPoint/buyLikepoint/showQRBuyLikepoint.dart';
import 'package:likewallet/view/transferPoint/buyLikepoint/uploadBankSlip.dart';

class Confirmbuylikepoint extends StatefulWidget {
  const Confirmbuylikepoint({super.key});

  @override
  State<Confirmbuylikepoint> createState() => _ConfirmbuylikepointState();
}

class _ConfirmbuylikepointState extends State<Confirmbuylikepoint> {

  late final buyLikepointCtrl;
  late final profileCtrl;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (Get.isRegistered<BuyLikepointController>()) {
      buyLikepointCtrl = Get.find<BuyLikepointController>();
    } else {
      buyLikepointCtrl = Get.put(BuyLikepointController());
    }
    if (Get.isRegistered<ProfileController>()) {
      profileCtrl = Get.find<ProfileController>();
    } else {
      profileCtrl = Get.put(ProfileController());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Container(
        height: 0.14.sh,
        width: 1.sw,
        color: const Color(0xffFFFFFF),
        padding: EdgeInsets.symmetric(
          horizontal: 0.05.sw,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 0));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 0)));
              },
              icon: Icon(IconHome.path_43609,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 1));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 1)));

              },
              icon: Icon(IconHome.path_43608,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 2));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 2)));

              },
              icon: Icon(IconHome.group_24548,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                /// ย้ายไปไลน์ โอ๋เอ๋

                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ContactUsPage()));
              },
              icon: Icon(IconHome.path_58781,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
              buyLikepointCtrl.hideSymbolDropdown();
            },
            child: Stack(
              alignment: Alignment.topLeft,
              clipBehavior: Clip.none,
              fit: StackFit.passthrough,
              children: [
                _body(),
                _head(context),
              ],
            )),
      ),
    );
  }

  Widget _head(context) {
    return Stack(
      children: [
        Container(
          color: const Color(0xff141322),
          height: 97.h,
          child: Container(
            padding: EdgeInsets.only(bottom: 12.5.h),
            alignment: Alignment.bottomCenter,
            width: 1.sw,
            child: Text(
              'buylike_CONFIRM'.tr,
              style: TextStyle(
                  letterSpacing: 0.5,
                  fontFamily: 'Proxima Nova',
                  color: const Color(0xffFFFFFF).withOpacity(1),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ),
        Container(
          height: 1.sh,
        ),
        Positioned(
          top: 0.086.sh,
          child: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
                height: 0.05.sh,
                width: 0.16.sw,
                decoration: BoxDecoration(
                  color: const Color(0xffB4E60D),
                  borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(40.0),
                      topRight: Radius.circular(40.0)),
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 9,
                      color: const Color(0xff707071).withOpacity(0.1),
                      offset: const Offset(
                        0.0,
                        3.0,
                      ),
                    ),
                  ],
                ),
                alignment: Alignment.centerLeft,
                // margin: EdgeInsets.only(left: ),
                padding: EdgeInsets.symmetric(
                    vertical: mediaQuery(context, 'height', 32.2),
                    horizontal: mediaQuery(context, 'width', 71.3)),
                child: Image.asset(
                  height: 0.08.sh,
                  width: 0.17.sw,
                  LikeWalletImage.icon_back_button,
                )),
          ),
        ),
      ],
    );
  }

  Widget _body(){
    return Container(
      padding: EdgeInsets.only(left: 0.2.sw),
      height: 1.sh,
      width: 1.sw,
      color: Color(0xffF5F5F5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 0.17.sh),
          Text(
            'buylike_Name'.tr,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 16.sp,
              fontWeight: FontWeight.normal,
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
            ),
          ),
          Text(
            '${profileCtrl.resProfile["firstName"]} ${profileCtrl.resProfile["lastName"]}',
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 16.sp,
              fontWeight: FontWeight.normal,
              color: LikeWalletAppTheme.gray.withOpacity(0.6),
            ),
          ),
          SizedBox(height: 20.h),
          Text(
            'buylike_GET'.tr,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 16.sp,
              fontWeight: FontWeight.normal,
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
            ),
          ),
          Text(
            '${buyLikepointCtrl.f
                .format((double.parse(buyLikepointCtrl.amountValue.value) * buyLikepointCtrl.rate.value))
                .toString()} LIKE',
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 16.sp,
              fontWeight: FontWeight.normal,
              color: LikeWalletAppTheme.gray.withOpacity(0.6),
            ),
          ),
          SizedBox(height: 20.h),
          Text(
            'buylike_PAID'.tr,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 16.sp,
              fontWeight: FontWeight.normal,
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
            ),
          ),
          Text(
            '${buyLikepointCtrl.amountSend.text} BAHT',
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 16.sp,
              fontWeight: FontWeight.normal,
              color: LikeWalletAppTheme.gray.withOpacity(0.6),
            ),
          ),
          SizedBox(height: 20.h),
          Container(
            width: 1.sw,
            height: 0.1.sh,
            // color: Colors.red,
            alignment: Alignment.centerRight,
            padding: EdgeInsets.only(right: 0.07.sw),
            child: InkWell(
              onTap: () {
                if(buyLikepointCtrl.selectPage.value == 'QRCode'){
                  // Get.to(() => QRCodePromptpay());
                  buyLikepointCtrl.generateQR();
                }else{
                  Get.to(() => UploadBankSlip());
                }
              },
              child: Container(
                alignment: Alignment.center,
                height: MediaQuery.of(context).size.height *
                    0.***********, // height of the button
                width:
                MediaQuery.of(context).size.height * 0.***********, //
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xff2B3038).withOpacity(1),
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 10,
                      color: LikeWalletAppTheme.black.withOpacity(0.3),
                      offset: Offset(
                        0.0,
                        5.0,
                      ),
                    ),
                  ],
                ),
                child: Text(
                  'bankingTran_button'.tr,
                  style: TextStyle(
                      color: Color(0xffB4E60D),
                      letterSpacing: 1,
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.normal,
                      fontSize: mediaQuery(context, 'height', 39)),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
