import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/like2crypto/faceAuthen.dart';
import 'package:likewallet/view/like2crypto/popupTermPolicy.dart';

class SlipComfirmation extends StatefulWidget {
  final String firstName;
  final String lastName;
  const SlipComfirmation({required this.firstName, required this.lastName});

  @override
  State<SlipComfirmation> createState() => _SlipComfirmationState();
}

class _SlipComfirmationState extends State<SlipComfirmation> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        color: Color(0xFF2B2A38),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              height: mediaQuery(context, "height", 226),
              width: MediaQuery.of(context).size.width * 0.88,
              alignment: Alignment.bottomCenter,
              child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                Image.asset(LikeWalletImage.icon_title_name, height: 72.07.h),
                SizedBox(width: 26.w),
                Text(
                  widget.firstName + " " + widget.lastName,
                  style: TextStyle(
                    fontFamily: 'Proxima Nova',
                    fontSize: 39.h,
                    color: const Color(0x4dffffff),
                    // letterSpacing: 1.17.,
                  ),
                  textAlign: TextAlign.left,
                ),
              ]),
            ),
            Container(
                width: MediaQuery.of(context).size.width * 0.9,
                height: MediaQuery.of(context).size.height * 0.85,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.75,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          SizedBox(height: 64.h),
                          Container(
                            width: MediaQuery.of(context).size.width * 0.8,
                            alignment: Alignment.centerLeft,
                            child: Text(
                              'Gold Withdrawal Confirmation',
                              style: TextStyle(
                                fontSize: 16,
                                fontFamily: 'Proxima Nova',
                                color: Color(0xFF8D8D8D),
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                          SizedBox(height: 64.h),
                          Container(
                              width: MediaQuery.of(context).size.width * 0.8,
                              padding: EdgeInsets.only(
                                  top: 32.h, bottom: 32.h, left: 10, right: 10),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Color(0xFF81D1B2014).withOpacity(0.08),
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: 16.h),
                                  Text(
                                    "Full name",
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Proxima Nova',
                                        color:
                                            Color(0xFF1A1818).withOpacity(0.4)),
                                  ),
                                  Text(
                                    "firstName + lastName",
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Proxima Nova',
                                        color: Color(0xFF666570)),
                                  ),
                                  SizedBox(height: 32.h),
                                  Text(
                                    "Address",
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Proxima Nova',
                                        color:
                                        Color(0xFF1A1818).withOpacity(0.4)),
                                  ),
                                  Text(
                                    "50/11 Thachang Sub-district, ",
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Proxima Nova',
                                        color: Color(0xFF666570)),
                                  ),
                                  Text(
                                    " Mueang District, Chanthaburi Province",
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Proxima Nova',
                                        color: Color(0xFF666570)),
                                  ),
                                  Text(
                                    "22000, Thailand",
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Proxima Nova',
                                        color: Color(0xFF666570)),
                                  ),
                                  Text(
                                    "Tel: 081-1234-567",
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Proxima Nova',
                                        color: Color(0xFF666570)),
                                  ),
                                  SizedBox(height: 32.h),
                                  Divider(color: Color(0xFF666570)),
                                  SizedBox(height: 32.h),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        "Will be duducted.",
                                        style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF8D8D8D)),
                                      ),
                                      Text(
                                        "1.15 G (gram)",
                                        style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF8D8D8D)),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 24.h),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        "Amount To receive",
                                        style: TextStyle(
                                            fontSize: 14,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF8D8D8D),
                                            fontWeight: FontWeight.w400),
                                      ),
                                      Text(
                                        "1.00 G (gram)",
                                        style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF8D8D8D)),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 32.h),
                                ],
                              )),
                          SizedBox(height: 64.h),
                          Container(
                            width: MediaQuery.of(context).size.width * 0.75,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Processing takes 7–14 business days with EMS delivery and insurance up to THB 50,000. Fees apply.',
                                  style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Proxima Nova',
                                      color:
                                          Color(0xFFB3B3B3)),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      useSafeArea: false,
                                      barrierColor: Colors.black.withOpacity(0.5),
                                      builder: (_) => PopupTermPolicy(),
                                    );
                                  },
                                  child: Container(
                                    height: 128.h,
                                    width: mediaQuery(context, 'width', 300),
                                    alignment: Alignment.centerLeft,
                                    // color: Colors.red.withOpacity(0.5),
                                    child: Text(
                                      'Read full terms',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w700,
                                        fontFamily: 'Proxima Nova',
                                        color: Color(0xFF2FA2FA),
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Container(
                        padding: EdgeInsets.only(
                            bottom: mediaQuery(context, 'height', 100)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: Container(
                                  width: mediaQuery(context, 'width', 930) / 2,
                                  height: mediaQuery(context, 'height', 133),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: <Widget>[
                                      Image.asset(
                                        LikeWalletImage
                                            .icon_button_cancel_white,
                                        height:
                                            mediaQuery(context, 'height', 133),
                                      ),
                                      Text(
                                        'Back',
                                        style: TextStyle(
                                            fontFamily: 'Proxima Nova',
                                            fontSize: mediaQuery(
                                                context, 'height', 36),
                                            color: LikeWalletAppTheme.gray
                                                .withOpacity(0.8)),
                                        textAlign: TextAlign.right,
                                      )
                                    ],
                                  )),
                            ),
                            GestureDetector(
                              onTap: () {
                                // AppRoutes.makeFirst(
                                //     context, HomeLikewallet(selectPage: 2));
                                Navigator.push(context, MaterialPageRoute(
                                    builder: (context) => FaceAuthen()));
                              },
                              child: Container(
                                  width: mediaQuery(context, 'width', 930) / 2,
                                  height: mediaQuery(context, 'height', 133),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: <Widget>[
                                      Text(
                                        'Accept and confirm',
                                        style: TextStyle(
                                            fontFamily: 'Proxima Nova',
                                            fontSize: mediaQuery(
                                                context, 'height', 36),
                                            color: LikeWalletAppTheme.gray
                                                .withOpacity(0.8)),
                                        textAlign: TextAlign.right,
                                      ),
                                      Image.asset(
                                        LikeWalletImage.icon_button_next_black,
                                        height:
                                            mediaQuery(context, 'height', 133),
                                      ),
                                    ],
                                  )),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
