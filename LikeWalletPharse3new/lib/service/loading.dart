import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_indicator/loading_indicator.dart';

class CustomLoading extends StatefulWidget {
  const CustomLoading({super.key});

  @override
  _CustomLoadingState createState() => _CustomLoadingState();
}

class _CustomLoadingState extends State<CustomLoading> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2), // ลดจาก 10 วินาทีเพื่อให้เร็วขึ้น
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // ป้องกันการกดย้อนกลับ
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 260.w, // ปรับจาก 600.w เป็น 260.w เพื่อให้เหมาะสม
              height: 260.w,
              child: LoadingIndicator(
                indicatorType: Indicator.ballClipRotate,
                colors: [const Color(0xFF29D8DA).withOpacity(1)],
              ),
            ),
          ],
        ),
      ),
    );
  }
}