import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/translate/language/en.dart';
import 'package:likewallet/controller/translate/language/km.dart';
import 'package:likewallet/controller/translate/language/lo.dart';
import 'package:likewallet/controller/translate/language/th.dart';

class TransalationController extends GetxController {
  final List locale = [
    {'name': 'English', 'locale': const Locale('en', 'US')},
    {'name': 'Thailand', 'locale': const Locale('th', 'Th')},
    {'name': 'Cambodian', 'locale': const Locale('km', 'Km')},
    {'name': 'Lao', 'locale': const Locale('lo', 'La')},
  ];

  RxString location = "".obs;
  RxList listLanguage = [].obs;

  updateLanguage(Locale locale) {
    // Get.back();
    Get.updateLocale(locale);
  }

  checkLocation(index) {
    if (index == 0) {
      location.value = 'EN';
    } else if (index == 1) {
      location.value = "TH";
    } else if (index == 2) {
      location.value = 'KM';
    } else if (index == 3) {
      location.value = 'LO';
    }
    print('index: $index');
    print(location.value);
    update();
  }

  showListLanguage(appName) {
    switch (appName) {
      case 'aam':
        listLanguage.value = [
          {'name': 'English', 'locale': const Locale('en', 'US')},
          {'name': 'Thailand', 'locale': const Locale('th', 'Th')}
        ];
        // location.value = "Thailand";
        update();
        break;
      case 'rafco':
        listLanguage.value = [
          {'name': 'English', 'locale': const Locale('en', 'US')},
          {'name': 'Thailand', 'locale': const Locale('th', 'Th')},
          {'name': 'Cambodian', 'locale': const Locale('km', 'Km')}
        ];
        // location.value = "Cambodian";
        update();
        break;
      case 'rplc':
        listLanguage.value = [
          {'name': 'English', 'locale': const Locale('en', 'US')},
          {'name': 'Thailand', 'locale': const Locale('th', 'Th')},
          {'name': 'Lao', 'locale': const Locale('lo', 'La')}
        ];
        // location.value = "Lao";
        update();
        break;
    }
    // update();
  }


}

class TranslationsService extends Translations{
  @override
  Map<String,Map<String,String>> get keys =>{
    'km_KM':Km().messages,
    'en_US': US().messages,
    'th_TH': TH().messages,
    'lo_LA': Lo().messages,
  };
}