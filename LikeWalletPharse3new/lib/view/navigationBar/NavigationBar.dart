import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/login/index.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';

class NavigationBar extends StatefulWidget {
  _NavigationBar createState() => new _NavigationBar();
}

class _NavigationBar extends State<NavigationBar> {

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  int curIndex = 0;
  Widget build(BuildContext context) {
    return Container(
      // height: 290.h,
      height: mediaQuery(context, 'height', 218.4),
      color: Color(0xffF5F5F5),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            color: Color(0xffF5F5F5),
            height: mediaQuery(context, 'height', 218.4),
            width: MediaQuery.of(context).size.width,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 15.w),
            // margin: EdgeInsets.only(top: 55.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton.icon(
                  onPressed: () {
                    // AppRoutes.makeFirst(
                    //     context, HomeLikewallet(selectPage: 0));
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 0)));
                  },
                  icon: Icon(IconHome.path_43609,
                      size: mediaQuery(context, 'height', 60),
                      color: Color(0xffB3B3B4)),
                  label: Container(),
                ),
                TextButton.icon(
                  onPressed: () {
                    // AppRoutes.makeFirst(
                    //     context, HomeLikewallet(selectPage: 1));
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 1)));

                  },
                  icon: Icon(IconHome.path_43608,
                      size: mediaQuery(context, 'height', 60),
                      color: Color(0xffB3B3B4)),
                  label: Container(),
                ),
                TextButton.icon(
                  onPressed: () {
                    // AppRoutes.makeFirst(
                    //     context, HomeLikewallet(selectPage: 2));
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 2)));

                  },
                  icon: Icon(IconHome.group_24548,
                      size: mediaQuery(context, 'height', 60),
                      color: Color(0xffB3B3B4)),
                  label: Container(),
                ),
                TextButton.icon(
                  onPressed: () {
                    /// ย้ายไปไลน์ โอ๋เอ๋

                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                ContactUsPage()));
                  },
                  icon: Icon(IconHome.path_58781,
                      size: mediaQuery(context, 'height', 60),
                      color: Color(0xffB3B3B4)),
                  label: Container(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
