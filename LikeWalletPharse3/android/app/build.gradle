def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'com.google.gms.google-services'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

//release
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}


android {

    namespace 'likewallet.likewallet'
    compileSdkVersion 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "likewallet.likewallet"
        minSdkVersion 27
        maxSdkVersion 34
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        resConfigs "en", "th","vi","km","lo"
    }
    //release
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
//            signingConfig signingConfigs.debug
        }
    }

    aaptOptions {
        noCompress 'tflite'
        noCompress 'lite'
    }

    configurations {
        all {
            exclude group: 'com.scottyab', module: 'rootbeer-lib'
            exclude group: 'com.google.zxing'
        }
    }

}

flutter {
    source '../..'
}

configurations.all {
    resolutionStrategy {
        force 'com.journeyapps:zxing-android-embedded:4.3.0'
    }
}

dependencies {
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.facebook.android:facebook-android-sdk:8.1.0'
    implementation 'com.facebook.android:facebook-core:8.1.0'
    implementation 'com.facebook.android:facebook-android-sdk:8.1.0'
    implementation 'com.facebook.android:facebook-share:8.1.0'
    implementation 'com.facebook.android:facebook-places:5.15.3'
    implementation 'com.facebook.android:facebook-messenger:8.1.0'
    implementation 'com.facebook.android:facebook-applinks:8.1.0'
    implementation 'com.google.firebase:firebase-analytics:17.2.0'
}
