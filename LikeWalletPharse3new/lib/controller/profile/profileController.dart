import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart' as firebase_storage;
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:likewallet/controller/permissionCheck/permissionCheckController.dart';
import 'package:likewallet/controller/walletController/walletDataController.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:path/path.dart' as Path;

class ProfileController extends GetxController {

  final FirebaseAuth firebaseAuth = FirebaseAuth.instance;
  final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  final FirebaseFirestore fireStore = FirebaseFirestore.instance;
  final ImagePicker _picker = ImagePicker();

  // User information
  User? user;
  String tierLevel = "";
  String tier = '';
  var resProfile = {};

  // Profile information
  RxString firstName = "..loading".obs;
  RxString lastName = "".obs;
  RxString phoneNumber = "".obs;
  RxString currentPhone = '..loading'.obs;
  RxInt refers = 0.obs;

  // Profile image
  RxString uploadedFileURL = "noprofile".obs;
  Rx<File?> imageFile = Rx<File?>(null);

  // Text controllers
  final TextEditingController editFirstName = TextEditingController();
  final TextEditingController editLastName = TextEditingController();
  final TextEditingController editPhoneNumber = TextEditingController();
  final TextEditingController editEmail = TextEditingController();
  final TextEditingController editPhone = TextEditingController();
  final TextEditingController editFacebook = TextEditingController();
  final TextEditingController editLine = TextEditingController();

  // Loading state
  RxBool isSaving = false.obs;

  Future<void> getCurrentUser() async {
    try{
      print("getCurrentUser");
      final checkAboutCtrl = Get.find<CheckAboutController>();
      user = firebaseAuth.currentUser;

      print(user);

      final walletController = Get.find<WalletDataController>();

      if (user == null) throw Exception('No user found');
      update();
      await checkLogin();
      getName();
      checkAboutCtrl.initializeCheckBlackList();
      walletController.getDataWallet(user!.phoneNumber);
    }catch(e){
      print("Error getting current user: $e");
    }
  }

  Future<String?> getTokenFirebase() async {
    User? user = firebaseAuth.currentUser;
    var token = await user!.getIdTokenResult();
    return token.token;
  }

  Future<void> getName() async {
    try{
      print("getName");
      print(user!.uid);

      await fireStore.collection('users').doc(user!.uid).get().then(
        (DocumentSnapshot doc) {
          if (doc.exists) {
            final data = doc.data() as Map<String, dynamic>;
            print(data);

            // Update reactive variables
            firstName.value = data['firstName'] ?? '';
            lastName.value = data['lastName'] ?? '';

            // Update text controllers
            editFirstName.text = data['firstName'] ?? '';
            editLastName.text = data['lastName'] ?? '';
            editEmail.text = data['email'] ?? '';
            editFacebook.text = data['facebook'] ?? '';
            editLine.text = data['line'] ?? '';

            // Update phone number
            phoneNumber.value = user!.phoneNumber.toString();
            editPhoneNumber.text = user!.phoneNumber.toString();
            editPhone.text = user!.phoneNumber.toString();

            // Update profile image
            uploadedFileURL.value = data['imageProfile'] ?? 'noprofile';

            // Store in resProfile for backward compatibility
            resProfile = {
              'firstName': firstName.value,
              'lastName': lastName.value,
            };

            print(resProfile);
          } else {
            print('No such document!');
          }
        },
      );

      // Get referrals count
      await fireStore.collection('users').doc(user!.uid).get().then((doc) {
        if (doc.exists) {
          final data = doc.data() as Map<String, dynamic>;
          final selfCode = data['selfCode'];

          if (selfCode != null) {
            fireStore
                .collection('users')
                .where('refCode', isEqualTo: selfCode)
                .get()
                .then((querySnapshot) {
              refers.value = querySnapshot.docs.length;
            });
          }
        }
      });

      update();
    } catch(e) {
      print("Error getting name: $e");
    }
  }

  Future<void> checkLogin() async {
    try {
      CheckAboutController checkAboutCtrl = Get.find<CheckAboutController>();
      // Handle blacklist cases
      if (checkAboutCtrl.blacklist == 'blackList' || checkAboutCtrl.blacklistLDX == 'blackList_LDX' || checkAboutCtrl.blacklistFIN == 'blackList_FIN') {
        tier = checkAboutCtrl.blacklist == 'blackList'
            ? checkAboutCtrl.blacklist
            : checkAboutCtrl.blacklistLDX == 'blackList_LDX'
            ? checkAboutCtrl.blacklistLDX
            : checkAboutCtrl.blacklistFIN;
        // Navigator.pushReplacement(
        //   context,
        //   MaterialPageRoute(builder: (_) => BlackListPage(page: '')),
        // );
        return;
      }

      // Check tier
      tier = await checkAboutCtrl.checkTier(
        email: user!.email,
        phone: user!.phoneNumber,
        list: 'whitelist',
      );

      print("tier.isNotEmpty");
      print(tier);

      if (tier.isNotEmpty) {
        tierLevel = tier;
        Storage.save(StorageKeys.tierLevel, tierLevel);
        if (tier == 'tier1') {
          await firebaseMessaging.subscribeToTopic('notifyTier1');
        } else if (tier == 'normal') {
          await firebaseMessaging.subscribeToTopic('notifyNormal');
        }
      }

      print("tier.isNotEmpty");
      print(tierLevel);
      // Check page permission
      final statusPage = await checkAboutCtrl.checkTierPermission(tierLevel: tierLevel, page: 'likewallet');

      if (statusPage.status == 'active') {
        // Navigate based on login status
        // Navigator.pushReplacement(
        //   context,
        //   MaterialPageRoute(
        //     builder: (_) => isLoggedIn ? PinProtect(redirectPage: 'HOME') : const IndexLike(),
        //   ),
        // );
      } else {
        // Navigate to maintenance page
        final lang = Get.locale?.languageCode ?? 'en';
        final detail = await checkAboutCtrl.selectLanguage(language: lang, detail: statusPage.detail);
        final title = await checkAboutCtrl.selectLanguage(language: lang, detail: statusPage.title);
        final detailTime = await checkAboutCtrl.selectLanguage(language: lang, detail: statusPage.detailTime);

        // Navigator.pushReplacement(
        //   context,
        //   MaterialPageRoute(
        //     builder: (_) => Maintenance(
        //       title: title,
        //       detail: detail,
        //       detailTime: detailTime,
        //       url: statusPage.url,
        //     ),
        //   ),
        // );

      }
      print('tierLevel: $tierLevel');
      update();
    } catch (e) {
      // Handle error
      // Storage.write('login', false);
      // Navigator.pushReplacement(
      //   context,
      //   MaterialPageRoute(builder: (_) => const IndexLike()),
      // );
    }
  }

  // Choose image from gallery
  Future<bool> chooseFile() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxHeight: 400,
        maxWidth: 400,
      );

      if (pickedFile != null) {
        imageFile.value = File(pickedFile.path);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('Error picking image: $e');
      return false;
    }
  }

  // Upload image to Firebase Storage
  Future<bool> uploadFile() async {
    if (imageFile.value == null) return false;

    isSaving.value = true;

    try {
      firebase_storage.Reference storageReference = firebase_storage
          .FirebaseStorage.instance
          .ref()
          .child('profile/${Path.basename(imageFile.value!.path)}}');

      final metadata = firebase_storage.SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {'picked-file-path': imageFile.value!.path});

      firebase_storage.UploadTask uploadTask =
          storageReference.putFile(File(imageFile.value!.path), metadata);

      final task = await uploadTask;

      if (task != null) {
        print('File Uploaded');
        final fileURL = await storageReference.getDownloadURL();
        uploadedFileURL.value = fileURL;

        print(uploadedFileURL.value);

        // Update user profile in Firestore
        if (user != null) {
          await fireStore
              .collection('users')
              .doc(user!.uid)
              .update({"imageProfile": uploadedFileURL.value});
        }

        isSaving.value = false;
        return true;
      } else {
        isSaving.value = false;
        return false;
      }
    } catch (e) {
      print('Error uploading file: $e');
      isSaving.value = false;
      return false;
    }
  }

  // Save profile information
  Future<void> saveProfile() async {
    isSaving.value = true;

    try {
      if (user == null) {
        isSaving.value = false;
        return;
      }

      await fireStore.collection('users').doc(user!.uid).update({
        'firstName': editFirstName.text,
        'lastName': editLastName.text,
        'email': editEmail.text,
        'facebook': editFacebook.text,
        'line': editLine.text
      });

      // Update local variables
      firstName.value = editFirstName.text;
      lastName.value = editLastName.text;

      // Show success message
      showColoredToast("Updated !", Colors.green);

      isSaving.value = false;
    } catch (e) {
      print('Error saving profile: $e');
      showColoredToast("Error updating profile", Colors.red);
      isSaving.value = false;
    }
  }

  // Show toast message
  void showColoredToast(String msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: color,
        textColor: Colors.white);
  }

  @override
  void onInit() {
    super.onInit();
    getCurrentUser();
  }

  @override
  void onClose() {
    // Dispose text controllers
    editFirstName.dispose();
    editLastName.dispose();
    editPhoneNumber.dispose();
    editEmail.dispose();
    editPhone.dispose();
    editFacebook.dispose();
    editLine.dispose();
    super.onClose();
  }
}