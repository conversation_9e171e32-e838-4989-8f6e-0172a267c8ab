import 'package:flutter/material.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';

class <PERSON>Authen extends StatefulWidget {
  const FaceAuthen({Key? key}) : super(key: key);

  @override
  State<FaceAuthen> createState() => _FaceAuthenState();
}

class _FaceAuthenState extends State<FaceAuthen> {


  bool completed = false;
  bool alert = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              color: Color(0xFF2B2A38),
          ),
          completed ? Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    completed = false;
                    setState(() {});
                  },
                  child: Image.asset('assets/image/like2crypto/complete_authen.png',
                    width: 170,
                    height: 170,
                  ),
                ),
                SizedBox(height: 50),
                Text('You face scan is complete',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 20,
                    fontFamily: 'Proxima Nova',
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ],
            ),
          ) :Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    completed = true;
                    setState(() {});
                  },
                  child: Image.asset('assets/image/like2crypto/face_frame.png',
                    width: 250,
                    height: 242,
                  ),
                ),
                SizedBox(height: 50),
                Text('Please scan your face to verify the transaction.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: 'Proxima Nova',
                    color: Color(0xFFB3B3B3),
                  ),
                ),
              ],
            ),
          ),
          if(alert && !completed) Positioned(
            bottom: 200,
            child: GestureDetector(
              onTap: () {
                alert = false;
                setState(() {});
              },
              child: Container(
                width: MediaQuery.of(context).size.width * 0.65,
                child: Text(
                  "Face recognition failed. Please try again or contact Customer Support.",
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Proxima Nova',
                    color: Color(0xFFFF0000),
                  ),
                ),
              ),
            ),
          ),
          if(completed) Positioned(
            bottom: 100,
            child: InkWell(
              onTap: () {
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 2)));
              },
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                height: MediaQuery.of(context).size.height * 0.06,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  gradient: LinearGradient(
                    begin: Alignment(-1.0, -0.94),
                    end: Alignment(1.0, 1.0),
                    colors: [
                      const Color(0xff52fcf0),
                      const Color(0xff33faec),
                      const Color(0xff22c4e6)
                    ],
                    stops: [0.0, 0.335, 1.0],
                  ),
                ),
                child: Center(
                  child: Text('Complete',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 20,
            child: GestureDetector(
              onTap: () {
                alert = true;
                setState(() {});
              },
              child: Container(
                child: Text(
                  'Powered by samsub',
                  style: TextStyle(
                    fontSize: 15,
                    fontFamily: 'Proxima Nova',
                    fontWeight: FontWeight.bold,
                    color: Color(0x88FFFFFF),
                  ),
                ),
              ),
            ),
          ),
        ],
      )
    );
  }
}
