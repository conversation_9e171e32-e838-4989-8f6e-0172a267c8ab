class PageMaintenance {
  final String status;
  final Map<String, String> title;
  final Map<String, String> detail;
  final Map<String, String> detailTime;
  final String url;

  PageMaintenance({
    required this.status,
    required this.title,
    required this.detail,
    required this.detailTime,
    required this.url,
  });

  factory PageMaintenance.fromJson(Map<String, dynamic> json) {
    return PageMaintenance(
      status: json['status'] ?? 'active',
      title: {'en': 'Title', 'th': 'หัวข้อ'},
      detail: {'en': 'Detail', 'th': 'รายละเอียด'},
      detailTime: {'en': 'Time', 'th': 'เวลา'},
      url: json['url'] ?? 'https://mock.url',
    );
  }
}