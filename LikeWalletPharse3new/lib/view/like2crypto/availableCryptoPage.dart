import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/service/components.dart';

class AvailablePage extends StatefulWidget {
  const AvailablePage({Key? key}) : super(key: key);

  @override
  State<AvailablePage> createState() => _AvailablePageState();
}

class _AvailablePageState extends State<AvailablePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              width: 1080.w,
              height: MediaQuery.of(context).size.height,
              child: Container(
                width: 1080.w,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.0, -1.0),
                      end: Alignment(0.0, 1.0),
                      colors: [
                        const Color(0xff1f2840),
                        const Color(0xff1e1d34)
                      ],
                      stops: [0.0, 1.0],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x33000000),
                        offset: Offset(-9, 0),
                        blurRadius: 25,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              top: mediaQuery(context, 'height', 185),
              right: -(mediaQuery(context, 'height', 440)),
              child: Container(
                height: MediaQuery.of(context).size.height * 0.72,
                child: Opacity(
                  opacity: 0.5,
                  child: Image.asset(
                    LikeWalletImage.home_bg_2,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              // color: Colors.red.withOpacity(0.5),
              child: Column(
                // mainAxisAlignment: MainAxisAlignment.center,

                children: [
                  SizedBox(
                    height: mediaQuery(context, 'height', 171),
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: Container(
                                width: mediaQuery(context, 'width', 100),
                                height: mediaQuery(context, 'width', 100),
                                // color: Colors.red.withOpacity(0.5),
                                alignment: Alignment.centerLeft,
                                child: Icon(
                                  Icons.arrow_back_ios_rounded,
                                  color: Color(0xFF504F56),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: mediaQuery(context, 'width', 30),
                            ),
                            Text(
                              "Available",
                              style: TextStyle(
                                color: Colors.blue,
                                fontSize: 18,
                                  fontFamily: 'Proxima Nova'
                              ),
                            ),
                          ],
                        ),
                        Container()
                      ],
                    ),
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'height', 90),
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.95,
                    height: MediaQuery.of(context).size.height * 0.17,
                    decoration: BoxDecoration(
                      color: Color(0xFF2C395BCC).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20)
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Container(
                          width: MediaQuery.of(context).size.width * 0.8,
                          height: (MediaQuery.of(context).size.height * 0.17) / 2,
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text("Assets Value",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                    fontFamily: 'Proxima Nova'
                                ),
                              ),
                              Text("Update 28 Jan 2025 - 03:26 PM",
                                style: TextStyle(
                                    color: Colors.white.withOpacity(0.5),
                                    fontSize: 12,
                                    fontFamily: 'Proxima Nova'
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width * 0.8,
                          height: (MediaQuery.of(context).size.height * 0.17) / 2,
                          alignment: Alignment.topCenter,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text("9,960,754,048.09",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 24,
                                    fontFamily: 'Proxima Nova'
                                ),
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Text("LIKE",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 15,
                                    fontFamily: 'Proxima Nova'
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.95,
                    height: MediaQuery.of(context).size.height * 0.1,
                    decoration: BoxDecoration(
                        color: Color(0xFF2C395BCC).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20)
                    ),
                    alignment: Alignment.center,
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.8,
                        height: MediaQuery.of(context).size.height * 0.1,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            height: MediaQuery.of(context).size.height * 0.1,
                            child: Row(
                              children: [
                                Container(
                                  width: 35,
                                  height: 35,
                                  child: Image.asset(
                                    LikeWalletImage.icon_btc,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                SizedBox(
                                  width: 10,
                                ),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("Bitcoin",
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontFamily: 'Proxima Nova',
                                        fontWeight: FontWeight.w700
                                      ),
                                    ),
                                    Text("3,333,000" + " THB",
                                      style: TextStyle(
                                          color: Colors.white.withOpacity(0.5),
                                          fontSize: 12,
                                          fontFamily: 'Proxima Nova'
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                          Container(
                            height: MediaQuery.of(context).size.height * 0.1,
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Row(
                                    children: [
                                      Text("4.75271",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 18,
                                            fontFamily: 'Proxima Nova'
                                        ),
                                      ),
                                      SizedBox(
                                        width: 10,
                                      ),
                                      Container(
                                        width: MediaQuery.of(context).size.width * 0.1,
                                        alignment: Alignment.centerLeft,
                                        child: Text("BTC",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 18,
                                              fontFamily: 'Proxima Nova'
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                  Text("+ 4.03%",
                                    style: TextStyle(
                                        color: Colors.green,
                                        fontSize: 16,
                                        fontFamily: 'Proxima Nova'
                                    ),
                                  ),
                                ]
                            ),
                          )
                        ],
                      )
                    ),
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.95,
                    height: MediaQuery.of(context).size.height * 0.1,
                    decoration: BoxDecoration(
                        color: Color(0xFF2C395BCC).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20)
                    ),
                    alignment: Alignment.center,
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.8,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 35,
                                height: 35,
                                child: Image.asset(
                                  LikeWalletImage.icon_gold,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("Gold",
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontFamily: 'Proxima Nova',
                                        fontWeight: FontWeight.w700
                                    ),
                                  ),
                                  Text("44,450" + " THB",
                                    style: TextStyle(
                                        color: Colors.white.withOpacity(0.5),
                                        fontSize: 12,
                                        fontFamily: 'Proxima Nova'
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                          Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Row(
                                children: [
                                  Text("0.05271",
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontFamily: 'Proxima Nova'
                                    ),
                                  ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Container(
                                    width: MediaQuery.of(context).size.width * 0.1,
                                    alignment: Alignment.centerLeft,
                                    child: Text("g.",
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontFamily: 'Proxima Nova'
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 16.h,
                              ),
                              Text("- 1.23%",
                                style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 16,
                                    fontFamily: 'Proxima Nova'
                                ),
                              ),
                            ]
                          )
                        ],
                      ),
                    )
                  )
                ],
              ),
            )
          ],
        ),
      )

    );
  }
}
