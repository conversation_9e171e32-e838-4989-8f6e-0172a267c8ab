import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';

class StatementController extends GetxController {
  var selectedRadio = 'All'.obs;
  var selectedDate = Rxn<DateTime>();

  String get formattedDate {
    if (selectedDate.value == null) return '';
    return DateFormat('d MMM yyyy').format(selectedDate.value!);
  }

  void pickDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      selectedDate.value = picked;
    }
  }
}