import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'dart:io';

import 'package:likewallet/model/cashOutModel/cashRateTextModel.dart';

class BankCashOutController extends GetxController {
  // Variables moved from the UI
  RxBool show = false.obs;
  final fireStore = FirebaseFirestore.instance;
  RxString typePayment = RxString('');
  RxBool promptpay = false.obs;
  RxBool truemoney = false.obs;
  OverlayEntry? overlayEntry;
  FocusNode promptPayFocusNode = FocusNode();
  final TextEditingController nameAccount = TextEditingController();
  final TextEditingController numberPromptpay = TextEditingController();
  final TextEditingController numberTruemoney = TextEditingController();
  var cashRateText = CashRateText('', '', '');


  // late OnLanguage onLanguage;
  RxString cashTimeout = RxString('');

  // Method to show overlay - UI-related call exists
  void showOverlayMethod() {
    // This method needs to be called from the UI since it requires BuildContext
  }

  // Method to remove overlay
  void removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  // Change payment method
  void changePayment(String value) {
    typePayment.value = value;
    update();
  }

  // Get cash out rate
  Future<void> getRateCashOut() async {
    try {
      String lang = Get.locale?.languageCode ?? 'en';

      var doc = await FirebaseFirestore.instance
          .collection('allText')
          .doc('cashText')
          .get();

      if (doc.exists && doc.data() != null) {
        cashRateText = CashRateText.fromJson(doc.data()!);
      } else {
        // handle กรณีไม่มีข้อมูล
        cashRateText = CashRateText('', '', '');
      }


      await FirebaseFirestore.instance
          .collection('allText')
          .doc('cash')
          .collection(lang)
          .doc('chooseMethod')
          .get()
          .then((value) {
        cashTimeout.value = value.data()!['cash_timeout'];
      });
    } catch (e) {
      print('Error getting rate cash out: $e');
    }
    update();
  }

  // Validate and submit promptpay
  String? onSubmit(String value, BuildContext context) {
    // UI-related call exists
    String pattern = r'(^(?:[0]9)?[0-9]{10,}$)';
    RegExp regex = RegExp(pattern);
    
    if (value.isEmpty) {
      return 'Please enter mobile number';
    } else if (!regex.hasMatch(value)) {
      return 'Please enter valid mobile number';
    } else if (regex.hasMatch(value)) {
      if (value.length < 10) {
        return 'Please check your promptpay number';
      }
      return null; // Valid input
    }
    return 'Invalid input';
  }

  // Toggle UI states
  void togglePromptpay(bool value) {
    promptpay.value = value;
    if (value) {
      truemoney.value = false;
      show.value = false;
    }
    update();
  }

  void toggleTruemoney(bool value) {
    truemoney.value = value;
    if (value) {
      promptpay.value = false;
      show.value = false;
    }
    update();
  }

  void toggleShow(bool value) {
    show.value = value;
    if (value) {
      promptpay.value = false;
      truemoney.value = false;
    }
    update();
  }

  // Remove bank account
  Future<bool> removeBankAccount(String documentID) async {
    try {

      await fireStore
          .collection('kycCashOut')
          .doc('bookBank')
          .collection(FirebaseAuth.instance.currentUser!.uid)
          .doc(documentID)
          .delete();
      
      return true;
    } catch (e) {
      print('Error removing bank account: $e');
      return false;
    }
  }
}
