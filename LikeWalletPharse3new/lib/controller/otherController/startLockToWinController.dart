import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/permissionCheck/permissionCheckController.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';

class StartLockToWin {
  // Dependencies
  Future<dynamic> startLockToWin(context, tierlevel) async {
    try {
      final FirebaseAuth firebaseAuth = FirebaseAuth.instance;
      // Check if user can access lotto page
      final canAccessLotto = await checkLottoPage(tierlevel);
      if (!canAccessLotto) {
        return null;
      }

      // Get language
      final language = Storage.get<String>(StorageKeys.language) ?? 'en';
      final langCode = language == 'th' ? 'th' : 'en';

      // Get Firebase token
      final firebaseTokenResult = await firebaseAuth.currentUser!.getIdTokenResult();
      final idToken = firebaseTokenResult.token;

      // Request custom token from server
      final response = await AppApi.post(
          '${AppEnv.apiUrl}/createCustomToken',
          {"token": idToken}
      );

      var mnemonic = await Storage.get(StorageKeys.mnemonic);
      var address = await Storage.get(StorageKeys.addressETH);

      // Prepare data for LockToWin
      Map<String, dynamic> data = {
        'token': response['token'],
        'lang': langCode,
        "mnemonic": mnemonic,
        "address": address,
      };

      print(data);

      // Start LockToWin
      return data;
    } catch (e) {
      // Handle error (can be expanded based on requirements)
      rethrow;
    }
  }

  Future<bool> checkLottoPage(tierlevel) async {
    try {
      final checkCtrl = Get.find<CheckAboutController>();

      // Check page permission
      final statusPage = await checkCtrl.checkTierPermission(tierLevel: tierlevel, page: 'lockToWin');

      if (statusPage.status == 'active') {
        return true;
      }

      // Handle maintenance page
      final lang = Storage.get(StorageKeys.language) ?? 'en';
      final title = await checkCtrl.selectLanguage(
        language: lang,
        detail: statusPage.title,
      );
      final detail = await checkCtrl.selectLanguage(
        language: lang,
        detail: statusPage.detail,
      );
      final detailTime = await checkCtrl.selectLanguage(
        language: lang,
        detail: statusPage.detailTime,
      );

      // Navigate to maintenance page
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (context) => CloseMaintenance(
      //       title: title,
      //       detail: detail,
      //       detailTime: detailTime,
      //       url: statusPage.url,
      //     ),
      //   ),
      // );

      return false;
    } catch (e) {
      // Handle error (can be expanded based on requirements)
      rethrow;
    }
  }


}