class Contacts {
  var name;
  var phoneNumber;
  var address;
  var color;

  Contacts(
      this.name,
      this.phoneNumber,
      this.address,
      this.color,
      );

  Contacts.fromJson(Map<String, dynamic> json)
      : name = json['name'],
        phoneNumber = json['phoneNumber'],
        address = json['address'],
        color = json['color'];

  Map<String, dynamic> toJson() => {
    'name': name,
    'phoneNumber': phoneNumber,
    'address': address,
    'color': color,
  };
}
